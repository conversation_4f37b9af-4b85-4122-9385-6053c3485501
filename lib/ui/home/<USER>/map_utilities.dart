import 'dart:math' as math;
import 'dart:ui' as ui;
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapUtilities {
  /// Calculate distance between two coordinates in kilometers
  static double calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371;
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);

    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    double distance = earthRadius * c;

    return distance;
  }

  /// Convert degrees to radians
  static double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// Convert GeoPoint to LatLng
  static LatLng? getLatLng(GeoPoint? location) {
    if (location == null) return null;
    return LatLng(location.latitude, location.longitude);
  }

  /// Load custom marker icon from assets
  static Future<BitmapDescriptor?> loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      return BitmapDescriptor.bytes(markerIcon);
    } catch (e) {
      log("Error loading custom marker icon: $e");
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  /// Get bytes from asset for custom marker
  static Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  /// Create a marker with default styling
  static Marker createMarker({
    required String markerId,
    required LatLng position,
    BitmapDescriptor? icon,
    String? title,
    String? snippet,
    VoidCallback? onTap,
    double? rotation,
    Offset? anchor,
    double? zIndex,
  }) {
    return Marker(
      markerId: MarkerId(markerId),
      position: position,
      icon: icon ?? BitmapDescriptor.defaultMarker,
      infoWindow: InfoWindow(title: title ?? "", snippet: snippet ?? ""),
      onTap: onTap,
      rotation: rotation ?? 0.0,
      anchor: anchor ?? const Offset(0.5, 1.0),
      zIndex: zIndex ?? 0.0,
    );
  }

  /// Create a polyline with default styling
  static Polyline createPolyline({
    required String polylineId,
    required List<LatLng> points,
    Color? color,
    int? width,
  }) {
    return Polyline(
      polylineId: PolylineId(polylineId),
      points: points,
      color: color ?? Colors.blue,
      width: width ?? 5,
    );
  }
}
