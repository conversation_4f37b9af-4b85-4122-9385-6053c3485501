import 'package:flutter/material.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>/order_list_item.dart';
import 'package:emartdriver/ui/home/<USER>/map_utilities.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MultipleOrdersDialog extends StatelessWidget {
  final List<OrderModel> orders;
  final LatLng? currentPosition;
  final Function(OrderModel) onOrderSelected;

  const MultipleOrdersDialog({
    super.key,
    required this.orders,
    required this.currentPosition,
    required this.onOrderSelected,
  });

  static void show({
    required BuildContext context,
    required List<OrderModel> orders,
    required LatLng? currentPosition,
    required Function(OrderModel) onOrderSelected,
  }) {
    showModalBottomSheet(
      enableDrag: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return MultipleOrdersDialog(
          orders: orders,
          currentPosition: currentPosition,
          onOrderSelected: onOrderSelected,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.70,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDragHandle(),
          const SizedBox(height: 20),
          _buildTitle(),
          const SizedBox(height: 10),
          _buildOrderCountContainer(context),
          const SizedBox(height: 15),
          _buildOrdersList(),
          const SizedBox(height: 10),
          _buildCancelButton(context),
        ],
      ),
    );
  }

  Widget _buildDragHandle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 20),
        GestureDetector(
          onTap: () {},
          child: Container(
            width: 50,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildTitle() {
    return Text(
      'Loja: ${orders.first.vendor.title}',
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Color(0xff425799),
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildOrderCountContainer(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Color.fromARGB(39, 201, 212, 247),
      ),
      width: MediaQuery.of(context).size.width,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset('assets/images/caixa.png', height: 30, width: 30),
          const SizedBox(width: 8),
          Text(
            "${orders.length} pedidos disponíveis",
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return Expanded(
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          final storePosition = MapUtilities.getLatLng(
              order.vendor.address_store?.location.geoPoint);
          double distanceToStore = 0;

          if (currentPosition != null && storePosition != null) {
            distanceToStore = MapUtilities.calculateDistance(
              currentPosition!.latitude,
              currentPosition!.longitude,
              storePosition.latitude,
              storePosition.longitude,
            );
          }

          return OrderListItem(
            order: order,
            distanceToStore: distanceToStore,
            onSelectOrder: () {
              Navigator.pop(context);
              onOrderSelected(order);
            },
          );
        },
      ),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.grey,
        foregroundColor: Colors.white,
        minimumSize: const Size(double.infinity, 45),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onPressed: () => Navigator.pop(context),
      child: const Text(
        'Cancelar',
        style: TextStyle(fontSize: 16),
      ),
    );
  }
}
