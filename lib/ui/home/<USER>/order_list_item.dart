import 'package:flutter/material.dart';
import 'package:emartdriver/model/OrderModel.dart';

class OrderListItem extends StatelessWidget {
  final OrderModel order;
  final double distanceToStore;
  final VoidCallback onSelectOrder;

  const OrderListItem({
    super.key,
    required this.order,
    required this.distanceToStore,
    required this.onSelectOrder,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: Color(0xff425799), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOrderHeader(),
            const SizedBox(height: 8),
            _buildCustomerInfo(),
            const SizedBox(height: 4),
            _buildAddressInfo(),
            const SizedBox(height: 4),
            _buildDistanceInfo(),
            const SizedBox(height: 12),
            _buildSelectButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Pedido #${order.id.substring(0, 8)}...',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: Color(0xff425799),
          ),
        ),
        Text(
          'R\$ ${order.price ?? "0,00"}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerInfo() {
    return Row(
      children: [
        const Icon(Icons.person, size: 16, color: Colors.grey),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            'Cliente: ${order.author.firstName} ${order.author.lastName}',
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  Widget _buildAddressInfo() {
    return Row(
      children: [
        const Icon(Icons.location_on, size: 16, color: Colors.redAccent),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            'Endereço: ${order.author.shippingAddress?.first.bairro ?? ""}',
            style: const TextStyle(fontSize: 14),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildDistanceInfo() {
    return Row(
      children: [
        const Icon(Icons.location_searching, size: 16, color: Colors.blueAccent),
        const SizedBox(width: 4),
        Text(
          'Distância até loja: ${distanceToStore.toStringAsFixed(1)} km',
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildSelectButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xff425799),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onPressed: onSelectOrder,
        child: const Text('Selecionar Pedido'),
      ),
    );
  }
}
