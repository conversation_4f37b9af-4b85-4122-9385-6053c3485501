import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:flutter/material.dart';

class OrderAcceptanceHandler {
  static Future<OrderModel?> acceptOrder({
    required String orderId,
    required String currentUserId,
    required BuildContext context,
  }) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      await docRef.update({
        "entregador_id": currentUserId,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      // API call commented out as in original code
      // try {
      //   final apiResult = await TaEntregueApiService.acceptOrder(
      //     idRemoto: orderId,
      //     idCliente: int.tryParse(order.authorID) ?? 0,
      //     idEntregador: int.tryParse(currentUserId) ?? 0,
      //     valor: order.price ?? "0,00",
      //   );

      //   if (!apiResult['success']) {
      //     log("Aviso: API externa retornou erro: ${apiResult['mensagem']}");
      //   } else {
      //     log("Pedido aceito na API externa com sucesso");
      //   }
      // } catch (apiError) {
      //   log("Erro ao chamar API externa: $apiError");
      // }

      final updatedDoc = await docRef.get();
      if (updatedDoc.exists) {
        return OrderModel.fromJson(updatedDoc.data()!);
      }
      return null;
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Erro ao aceitar o pedido.")),
        );
      }
      return null;
    }
  }

  static Future<bool> confirmReturn({
    required String orderId,
    required BuildContext context,
  }) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      await docRef.update({
        'status': OrderStatus.completed.description,
        'has_return': false,
      });

      return true;
    } catch (e) {
      log("Erro ao confirmar devolução: $e");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erro ao confirmar devolução: $e")),
        );
      }
      return false;
    }
  }
}
