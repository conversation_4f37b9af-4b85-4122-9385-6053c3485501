import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationManager {
  StreamSubscription<Position>? _positionStreamSubscription;
  LatLng? _currentPosition;
  double _currentHeading = 0.0;
  BitmapDescriptor? _deliveryPersonIcon;

  LatLng? get currentPosition => _currentPosition;
  double get currentHeading => _currentHeading;

  /// Initialize location services and get current position
  Future<LatLng?> determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          return null;
        }
      }

      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      _currentPosition = LatLng(position.latitude, position.longitude);
      log("Current position determined: ${position.latitude}, ${position.longitude}");

      return _currentPosition;
    } catch (e) {
      log("Error determining position: $e");
      return null;
    }
  }

  /// Start listening to position updates
  void startPositionUpdates({
    required Function(LatLng) onPositionUpdate,
  }) {
    _positionStreamSubscription?.cancel();

    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _positionStreamSubscription =
        Geolocator.getPositionStream(locationSettings: locationSettings)
            .listen((Position position) {
      _currentPosition = LatLng(position.latitude, position.longitude);
      _currentHeading = position.heading;
      onPositionUpdate(_currentPosition!);
    });
  }

  /// Stop position updates
  void stopPositionUpdates() {
    _positionStreamSubscription?.cancel();
  }

  /// Set delivery person icon
  void setDeliveryPersonIcon(BitmapDescriptor? icon) {
    _deliveryPersonIcon = icon;
  }

  /// Create current position marker
  Marker createCurrentPositionMarker() {
    if (_currentPosition == null) {
      throw Exception("Current position is null");
    }

    return Marker(
      markerId: const MarkerId('current_position'),
      position: _currentPosition!,
      icon: _deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
      infoWindow: const InfoWindow(title: "Minha Localização"),
      zIndex: 2,
      rotation: _currentHeading,
      anchor: const Offset(0.5, 0.5),
    );
  }

  /// Center map on current location
  void centerOnCurrentLocation({
    required GoogleMapController mapController,
    required BuildContext context,
  }) {
    if (_currentPosition == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Aguardando localização...'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      return;
    }

    try {
      mapController.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentPosition!,
            zoom: 17.0,
            tilt: 0,
          ),
        ),
      );
    } catch (e) {
      log("Erro ao centralizar mapa: $e");
    }
  }

  /// Zoom in on map
  void zoomIn(GoogleMapController mapController) {
    try {
      mapController.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom + 1.0;
        newZoom = newZoom > 20.0 ? 20.0 : newZoom;
        mapController.animateCamera(CameraUpdate.zoomTo(newZoom));
      });
    } catch (e) {
      log("Erro ao aumentar zoom: $e");
    }
  }

  /// Zoom out on map
  void zoomOut(GoogleMapController mapController) {
    try {
      mapController.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom - 1.0;
        newZoom = newZoom < 2.0 ? 2.0 : newZoom;
        mapController.animateCamera(CameraUpdate.zoomTo(newZoom));
      });
    } catch (e) {
      log("Erro ao reduzir zoom: $e");
    }
  }

  /// Dispose resources
  void dispose() {
    _positionStreamSubscription?.cancel();
  }
}
